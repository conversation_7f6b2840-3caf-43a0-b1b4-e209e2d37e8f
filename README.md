# 📚 Open Library Book Finder 

A modern, responsive React application for searching and discovering books using the Open Library API. Built with React 19, Vite, and Tailwind CSS.

![React Book Finder](https://img.shields.io/badge/React-19.1.1-blue.svg)
![Vite](https://img.shields.io/badge/Vite-7.1.3-646CFF.svg)
![TailwindCSS](https://img.shields.io/badge/TailwindCSS-4.1.12-38B2AC.svg)

## ✨ Features

- **🔍 Advanced Search**: Search books by title, author, subject, or ISBN
- **💡 Smart Autocomplete**: Real-time search suggestions with debounced API calls
- **📱 Responsive Design**: Optimized for desktop, tablet, and mobile devices
- **🎨 Modern UI**: Clean, intuitive interface with Tailwind CSS styling
- **📖 Rich Book Details**: Display book covers, author information, publication year, edition count, and subjects
- **🔗 External Links**: Direct links to Open Library for detailed book information
- **⚡ Performance Optimized**: Efficient pagination with "Load More" functionality
- **🎯 Error Handling**: Graceful error handling with user-friendly messages
- **♿ Accessibility**: ARIA labels and keyboard navigation support

## 🚀 Quick Start

### Prerequisites

- Node.js (version 16 or higher)
- npm, yarn, or pnpm

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd react-book-finder
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or
   yarn install
   # or
   pnpm install
   ```

3. **Start the development server**
   ```bash
   npm run dev
   # or
   yarn dev
   # or
   pnpm dev
   ```

4. **Open your browser**
   Navigate to `http://localhost:5173` to see the application.

## 📦 Available Scripts

- `npm run dev` - Start development server with hot reload
- `npm run build` - Build the application for production
- `npm run preview` - Preview the production build locally
- `npm run lint` - Run ESLint to check code quality

## 🏗️ Project Structure

```
src/
├── components/           # Reusable React components
│   ├── SearchBar.jsx    # Search input with autocomplete
│   ├── BookList.jsx     # Grid layout for book results
│   ├── BookCard.jsx     # Individual book display card
│   ├── LoadMoreButton.jsx # Pagination component
│   └── Autocomplete.jsx # Autocomplete functionality
├── api.js               # Open Library API integration
├── App.jsx              # Main application component
├── App.css              # Component-specific styles
├── index.css            # Global styles and Tailwind imports
└── main.jsx             # Application entry point
```

## 🔧 Technologies Used

### Core Technologies
- **React 19.1.1** - Modern React with latest features
- **Vite 7.1.3** - Fast build tool and development server
- **JavaScript (ES6+)** - Modern JavaScript features

### Styling & UI
- **Tailwind CSS 4.1.12** - Utility-first CSS framework
- **Lucide React** - Beautiful, customizable icons
- **CSS Grid & Flexbox** - Modern layout techniques

### Development Tools
- **ESLint** - Code linting and quality checks
- **Vite Plugin React** - React integration for Vite

## 🌐 API Integration

This application uses the [Open Library Search API](https://openlibrary.org/dev/docs/api/search) to fetch book data.

### API Features Used:
- **Search Endpoint**: `https://openlibrary.org/search.json`
- **Search Types**: Title, Author, Subject, ISBN
- **Pagination**: Offset-based pagination (20 results per page)
- **Cover Images**: High-quality book cover images
- **Rich Metadata**: Author names, publication years, edition counts, subjects

### Rate Limiting
The application implements:
- Debounced search suggestions (300ms delay)
- Error handling for API failures

## 🎨 UI/UX Features

### Search Experience
- **Multi-type Search**: Dropdown to select search type (Title, Author, Subject, ISBN)
- **Live Suggestions**: Real-time autocomplete with book previews
- **Visual Feedback**: Loading states and error messages
- **Keyboard Navigation**: Full keyboard accessibility

### Book Display
- **Card Layout**: Clean, information-rich book cards
- **Cover Images**: Fallback handling for missing covers
- **Edition Badges**: Visual indicators for multiple editions
- **External Links**: Direct access to Open Library pages

### Responsive Design
- **Mobile-First**: Optimized for mobile devices
- **Flexible Grid**: Responsive book grid layout

## 🔍 Search Types

1. **Title Search**: Find books by their title
2. **Author Search**: Search by author name
3. **Subject Search**: Browse books by subject/genre
4. **ISBN Search**: Look up specific editions by ISBN

## 🚀 Performance Optimizations

- **Debounced API Calls**: Reduces unnecessary API requests
- **Lazy Loading**: Efficient pagination with load more
- **Image Optimization**: Proper image loading and fallbacks
- **Bundle Optimization**: Vite's optimized production builds

## 🛠️ Development

### Code Style
- Consistent component structure and naming
- Modern React patterns (hooks, functional components)

### Component Architecture
- **Modular Design**: Reusable, single-responsibility components
- **Props Interface**: Clear prop definitions and defaults
- **State Management**: Efficient local state with React hooks


## 🙏 Acknowledgments

- [Open Library](https://openlibrary.org/) for providing the free book data API
- [Tailwind CSS](https://tailwindcss.com/) for the utility-first CSS framework
- [Lucide](https://lucide.dev/) for the beautiful icon set
- [Vite](https://vitejs.dev/) for the fast development experience

---

**Built with ❤️ using React and Open Library API**
